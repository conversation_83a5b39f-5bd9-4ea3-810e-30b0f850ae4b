var sidebarFull=200,sidebarCollapsed=80,LOGO_COLOR="#43a2ad",OLD_STUDENTS_FEMALE_COLOR="#bfe9ef",PAST_DAYS=1825;function registerSidebarMenu(){registerHomeMenu(),registerChangePassword(),menuLoader.registerSidebarMenu()}function registerHomeMenu(){$("#homeNav").on("click",function(){loadHomePage()})}function registerChangePassword(){$("#changePasswordNav").on("click",function(){loadChangePasswordPage()})}function loadHomePage(){ajaxClient.get("/organisation-portal/home",function(e){$("#main-content").html(e),$("input#stats-date").daterangepicker({autoApply:!0,singleDatePicker:!0,showDropdowns:!0,minDate:moment().startOf("day").subtract(PAST_DAYS,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"},onSelect:function(e){$(this).change()}}).on("change",function(){homePage.refreshHomePage()}),homePage.displayDashboardContent(),academicSessionHandler.bindSessionChangeEvent(homePage.refreshHomePage)})}$(document).ready(function(){registerSidebarMenu(),$("#sidebar").addClass("collapsed"),registerChangePassword(),$("#bell-notification-li").css("display","none")});var homePageV2={loadHomePage:function(){ajaxClient.get("/organisation-portal/home/<USER>",function(e){$("#main-content").html(e),initSelect2(),$("input#stats-date-range").daterangepicker({autoApply:!1,showDropdowns:!0,startDate:moment().startOf("day"),endDate:moment().startOf("day"),minDate:moment().startOf("day").subtract(PAST_DAYS,"days"),maxDate:moment().startOf("day"),locale:{format:inputDatePickerFormat,cancelLabel:"Clear"},ranges:{Today:[moment(),moment()],Yesterday:[moment().subtract(1,"days"),moment().subtract(1,"days")],"Last 7 Days":[moment().subtract(6,"days"),moment()],"Last 30 Days":[moment().subtract(29,"days"),moment()],"This Month":[moment().startOf("month"),moment().endOf("month")],"Last Month":[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")]}}).on("change",function(){homePageV2.loadWidgets()}),homePageV2.loadWidgets()})},loadWidget:function(e,t,a){for(var n in e)console.log("displaying loader for : "+n),homePageV2.displayWidgetLoader(e[n]);ajaxClient.get(t,function(t){for(var n in a(e,t),e)homePageV2.hideWidgetLoader(e[n])},!0)},loadWidgets:function(){var e=$("#select-institutes").val(),t=$("#stats-date-range").val(),a=getDate(t.split(" - ")[0]).getTime()/1e3,n=getDayStart(getDate(t.split(" - ")[1]).add(1,"days")).getTime()/1e3-1;console.log(e),console.log(a+" - "+n),homePageV2.loadWidget({studentAttendanceWidgetClass:".student-attendance-stats",studentCountWidgetClass:".student-count-stats",studentCountDistributionWidgetClass:".student-count-distribution-stats"},"/organisation-portal/stats/v2/student-attendance",homePageV2.renderStudentAttendanceAndMetadata),homePageV2.loadWidget({staffCountWidgetClass:".staff-count-stats",staffAttendanceWidgetClass:".staff-attendance-stats",staffGenderDistributionWidgetClass:".staff-gender-distribution-stats"},"/organisation-portal/stats/v2/staff-attendance",homePageV2.renderStaffAttendance),homePageV2.loadWidget({studentAdmissionWidgetClass:".student-admission-stats"},"/organisation-portal/stats/v2/student-admission",homePageV2.renderStudentAdmissionStats),homePageV2.loadWidget({totalFeeCollectionWidgetClass:".fee-collection-stats",feeCollectionPaymentModeDistributionWidgetClass:".fee-collection-payment-mode-distribution-stats",feeCollectionFeeHeadDistributionWidgetClass:".fee-collection-fee-head-distribution-stats"},"/organisation-portal/stats/v2/fee-collection",homePageV2.renderFeeCollectionStats)},displayWidgetLoader:function(e){$(e).find(".stats-widget-loader").attr("style","display:block;"),$(e).find(".stats-widget-content").attr("style","display:none;")},hideWidgetLoader:function(e){$(e).find(".stats-widget-loader").attr("style","display:none;"),$(e).find(".stats-widget-content").attr("style","display:block;")},renderStudentAttendanceAndMetadata:function(e,t){var a=e.studentAttendanceWidgetClass,n=e.studentCountWidgetClass;$(n).find(".stats-widget-content").text(t.totalStudent);studentAttendanceCounts=t.studentAttendanceCounts[0];for(var o=0;o<studentAttendanceCounts.attendanceCounts.length;o++)"PRESENT"==studentAttendanceCounts.attendanceCounts[o].attendanceStatus&&(studentAttendanceCounts.attendanceCounts[o].count,studentAttendanceCounts.attendanceCounts[o].totalCount),"LEAVE"==studentAttendanceCounts.attendanceCounts[o].attendanceStatus&&(studentAttendanceCounts.attendanceCounts[o].count,studentAttendanceCounts.attendanceCounts[o].totalCount);const s=$('\n        <div id="carouselExampleControls" class="carousel slide" data-ride="carousel">\n          <div class="carousel-inner"></div>\n          <a class="carousel-control-prev" href="#carouselExampleControls" role="button" data-slide="prev">\n            <span class="carousel-control-prev-icon" aria-hidden="true" style="background-image: url(\'data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'black\' viewBox=\'0 0 8 8\'%3E%3Cpath d=\'M5.25 0L3.82 1.41 6.41 4 3.82 6.59 5.25 8 9.25 4z\'/%3E%3C/svg%3E\');"></span>\n            <span class="sr-only">Previous</span>\n          </a>\n          <a class="carousel-control-next" href="#carouselExampleControls" role="button" data-slide="next">\n            <span class="carousel-control-next-icon" aria-hidden="true" style="background-image: url(\'data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'black\' viewBox=\'0 0 8 8\'%3E%3Cpath d=\'M2.75 0L4.18 1.41 1.59 4 4.18 6.59 2.75 8 -1.25 4z\'/%3E%3C/svg%3E\');"></span>\n            <span class="sr-only">Next</span>\n          </a>\n        </div>\n      '),r=s.find(".carousel-inner");t.studentAttendanceCounts.forEach((e,a)=>{var n=e.attendanceType;const o=e.attendanceCounts.find(e=>"PRESENT"===e.attendanceStatus),s=(e.attendanceCounts.find(e=>"LEAVE"===e.attendanceStatus),(o.count/o.total*100).toFixed(1)),d=$(`\n          <div class="carousel-item ${0===a?"active":""}">\n            <div class="card-body py-4">\n              <div class="media">\n                <div class="media-body">\n                  <h4 class="mb-2 stats-widget-content">${o.count} / ${o.total} (${s}%)</h4>\n                  <p class="mb-2">Student Attendance</p>\n                  <div class="mb-0 mt-3" >\n                    <span class="badge badge-soft-success" >\n                      <i class="mdi mdi-arrow-bottom-right"></i>\n                      ${n}\n                    </span>\n                  </div>\n                </div>\n                <div class="d-inline-block ml-3">\n                  <div class="stat">\n                    \x3c!-- <span class="iconify" data-icon="bx-bx-rupee" data-inline="false"></span> --\x3e\n                    <i class="align-middle text-success" data-feather="user-check"></i>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        `);r.append(d);var i=[],l=[],c=0;t.studentCount.forEach(e=>{var t=e.instituteName,a=e.value;i.push(t),l.push(a),c+=a}),homePageV2.renderStudentCountPieChart(i,l,c),homePageV2.renderStudentInstituteCountTable(i,l,c)}),$(a).find(".stats-widget-content").html(s),window.feather&&feather.replace()},renderStaffAttendance:function(e,t){var a=e.staffAttendanceWidgetClass,n=e.staffCountWidgetClass,o=0,s=0,r=0;if(t.staffAttendanceCounts&&t.staffAttendanceCounts.length>0){var d=t.staffAttendanceCounts[0].attendanceCounts,i=d.find(e=>"PRESENT"===e.attendanceStatus);i&&(o=i.count,r=i.total);var l=d.find(e=>"LEAVE"===e.attendanceStatus);l&&(s=l.count)}var c=[],u=[],h=[],g={},f=[...new Set(t.staffCountByGender.map(e=>e.instituteName))];f.forEach(e=>{g[e]={male:0,female:0}}),t.staffCountByGender.forEach(e=>{"MALE"===e.gender?g[e.instituteName].male=e.count:"FEMALE"===e.gender&&(g[e.instituteName].female=e.count)}),f.forEach(e=>{c.push(e),u.push(g[e].male),h.push(g[e].female)});$(n).find(".stats-widget-content").text(r),$(a).find(".stats-widget-content-staff-present").text(o),$(a).find(".stats-widget-content-staff-leave").text(s),homePageV2.renderStaffGenderDistributionChart(c,u,h)},renderStudentAdmissionStats:function(e,t){console.log(t),console.log("-----------------------");var a=e.studentAdmissionWidgetClass;$(a).find(".stats-widget-content-admission").text(t.totalNewAdmissions),$(a).find(".stats-widget-content-tc").text(t.totalTCIssued)},renderFeeCollectionStats:function(e,t){var a=e.totalFeeCollectionWidgetClass;$(a).find(".stat-value").text(formatINRCurrency(t.totalAmount));var n=[],o=[];t.paymentModeCollections&&Array.isArray(t.paymentModeCollections)&&t.paymentModeCollections.forEach(function(e){n.push(e.modeDisplayName),o.push(e.value)});var s=[],r=[],d=[],i={};if(t.instituteStats&&Array.isArray(t.instituteStats)){var l=[window.theme.success,window.theme.warning,window.theme.danger,window.theme.info,"#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb","#108F2B","#C08143","#DCDF69","#719E90","#A8B9DF","#B74034","#F196CD","#6486B9","#5551FA","#E8F697","#0591F6","#4C6C42","#442BC6"];t.instituteStats.forEach(function(e){s.push(e.instituteName),e.feeHeadCollections&&Array.isArray(e.feeHeadCollections)&&e.feeHeadCollections.forEach(function(e){-1===d.indexOf(e.feeHead)&&(d.push(e.feeHead),i[e.feeHead]=l[(d.length-1)%l.length])})}),d.forEach(function(e){var a=[];t.instituteStats.forEach(function(t){var n=0;if(t.feeHeadCollections&&Array.isArray(t.feeHeadCollections)){var o=t.feeHeadCollections.find(function(t){return t.feeHead===e});o&&(n=o.value)}a.push(n)}),r.push({label:e,backgroundColor:i[e],borderColor:i[e],hoverBackgroundColor:i[e],hoverBorderColor:i[e],data:a,barPercentage:.325,categoryPercentage:.5})})}homePageV2.renderFeePaymentModeCollectionPieChart(n,o,formatINRCurrency(t.totalAmount)),homePageV2.renderFeePaymentModeCollectionTable(n,o,t.totalAmount),homePageV2.renderFeeCollectionFeeHeadDistributionChart(s,r)},renderStudentCountPieChart:function(e,t,a){new Chart($("#chartjs-student-count-pie"),{type:"pie",data:{labels:e,datasets:[{data:t,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(e,t,n)=>{var o=e.chart.width,s=e.chart.height,r=e.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,l=Math.round((o-r.measureText(i).width)/2),c=s/2;r.fillText(i,l,c),r.save()}}]})},renderStudentInstituteCountTable:function(e,t,a){var n='<table class="table mb-0">';n+="<thead>",n+="<tr>",n+="<th>Institute</th>",n+='<th class="text-center">Student Count</th>',n+='<th class="text-center">Percentage</th>',n+="</tr>",n+="</thead>",n+="<tbody>";for(var o=["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],s=0;s<e.length;s++){var r=a>0?(t[s]/a*100).toFixed(1):0;n+="<tr>",n+='<td><i class="fas fa-square-full" style="color: '+o[s%o.length]+'"></i>&nbsp;&nbsp;'+e[s]+"</td>",n+='<td class="text-center">'+t[s]+"</td>",n+='<td class="text-center">'+r+"%</td>",n+="</tr>"}n+='<tr style="font-weight: bold;">',n+="<td>TOTAL</td>",n+='<td class="text-center">'+a+"</td>",n+='<td class="text-center">100%</td>',n+="</tr>",n+="</tbody>",n+="</table>",$(".student-institute-count-table").html(n)},renderFeePaymentModeCollectionTable:function(e,t,a){var n='<table class="table mb-0">';n+="<thead>",n+="<tr>",n+="<th>Payment Mode</th>",n+='<th class="text-center">Amount</th>',n+='<th class="text-center">Percentage</th>',n+="</tr>",n+="</thead>",n+="<tbody>";for(var o=["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],s=0;s<e.length;s++){var r=a>0?(t[s]/a*100).toFixed(1):0;n+="<tr>",n+='<td><i class="fas fa-square-full" style="color: '+o[s%o.length]+'"></i>&nbsp;&nbsp;'+e[s]+"</td>",n+='<td class="text-center">₹'+t[s].toLocaleString()+"</td>",n+='<td class="text-center">'+r+"%</td>",n+="</tr>"}n+='<tr style="font-weight: bold;">',n+="<td>TOTAL</td>",n+='<td class="text-center">₹'+a.toLocaleString()+"</td>",n+='<td class="text-center">100%</td>',n+="</tr>",n+="</tbody>",n+="</table>",$(".fee-collection-institute-table").html(n)},renderStaffGenderDistributionChart:function(e,t,a){new Chart($("#chartjs-staff-gender-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Male",backgroundColor:window.theme.success,borderColor:window.theme.success,hoverBackgroundColor:window.theme.success,hoverBorderColor:window.theme.success,data:t,barPercentage:.325,categoryPercentage:.5},{label:"Female",backgroundColor:window.theme.warning,borderColor:window.theme.warning,hoverBackgroundColor:window.theme.warning,hoverBorderColor:window.theme.warning,data:a,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderFeePaymentModeCollectionPieChart:function(e,t,a){new Chart($("#chartjs-fee-collection-payment-mode-pie"),{type:"pie",data:{labels:e,datasets:[{data:t,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(e,t,n)=>{var o=e.chart.width,s=e.chart.height,r=e.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,l=Math.round((o-r.measureText(i).width)/2),c=s/2;r.fillText(i,l,c),r.save()}}]})},renderFeeCollectionFeeHeadDistributionChart:function(e,t){console.log("Institute Labels:",e),console.log("Fee Head Datasets:",t),new Chart($("#chartjs-fee-collection-fee-head-distribution"),{type:"horizontalBar",data:{labels:e,datasets:t},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0,position:"top",align:"start",labels:{boxWidth:12}},scales:{xAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!0}],yAxes:[{stacked:!0,gridLines:{color:"transparent"}}]},tooltips:{mode:"index",intersect:!1,callbacks:{title:function(e,t){return"Institute: "+t.labels[e[0].index]},label:function(e,t){var a=t.datasets[e.datasetIndex].label||"";return a&&(a+=": "),a+=e.xLabel.toLocaleString()},footer:function(e,t){var a=0;return e.forEach(function(e){a+=e.xLabel}),"Total: "+a.toLocaleString()}}}}})}},homePage={refreshHomePage:function(){var e=academicSessionHandler.getSelectedSessionId(),t=getDate($("input#stats-date").val()).getTime()/1e3;ajaxClient.get("/organisation-portal/home-date-session-change/"+t+"/"+e,function(e){$("#attendance-dashboard-session-content").html(e),homePage.displayDashboardContent()})},displayDashboardContent:function(){var e=readJson("#home-page-stats"),t=[],a=[],n=[],o=[],s={},r=0,d=[],i=0,l=[],c=[],u=[],h=[],g=[],f=[],m=[];console.log("test");var p=[],C=0;for(instituteId in e){var b=e[instituteId];institute=b.institute;var v=institute.branchName,w=institute.instituteUniqueCode;null!=v&&""!=v||(v=institute.instituteName),t.push(v);var y=w+":"+v;n.push(y),a.push(b.total_students);var x=b.attendance_stats,P=0;null!=x&&(P=parseFloat(x.totalPresentAttendance.toFixed(2))),r+=P,d.push(parseFloat(P.toFixed(2)));var A=b.transport_stats,S=0;null!=A&&(S=A.totalTransportAssignedStudentCount),l.push(S),i+=S;var L=b.staff_stats;if(null!=L){C+=L.todayTotalPresentStaff,p.push(L.todayTotalPresentStaff);var k=L.staffGenderWiseCount;if(null!=k)for(var F=Object.keys(k),$=0;$<F.length;$++){var O=F[$];"MALE"===O?c.push(k[O]):"FEMALE"===O&&u.push(k[O])}}var E=b.class_payment_stats;if(null!=E){h.push(parseFloat(E.assignedAmount.toFixed(2))),g.push(parseFloat(E.collectedAmount.toFixed(2))),f.push(parseFloat(E.discountAmount.toFixed(2))),m.push(parseFloat(E.dueAmount.toFixed(2)));var R=E.feeHeadCollectedAmountMap;for(const[e,t]of Object.entries(R))s[e]||(o.push(e),s[e]={}),s[e][y]||(s[e][y]=0),s[e][y]+=t}else h.push(0),g.push(0),f.push(0),m.push(0)}homePage.renderStudentCountChart(t,a),homePage.renderPresentAttendancePieChart(t,d,r),homePage.renderStudentFeesCountChart(t,h,g,f,m),homePage.renderStudentFeeHeadsCountChart(o,n,s),homePage.renderTransportAssignedPieChart(t,l,i),homePage.renderStaffGenderCountChart(t,c,u),homePage.renderStaffTodayAttendanceChart(t,p,C)},renderStudentCountChart:function(e,t){new Chart($("#chartjs-student-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Student Count",backgroundColor:PRIMARY_LOGO_COLOR,borderColor:PRIMARY_LOGO_COLOR,hoverBackgroundColor:PRIMARY_LOGO_COLOR,hoverBorderColor:PRIMARY_LOGO_COLOR,data:t,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!1},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]},animation:{onComplete:function(){var e=this.chart,t=e.ctx;t.textAlign="center",t.fillStyle="rgba(0, 0, 0, 1)",t.textBaseline="bottom",this.data.datasets.forEach(function(a,n){e.controller.getDatasetMeta(n).data.forEach(function(e,n){var o=a.data[n];t.fillText(o,e._model.x,e._model.y-5)})})}},events:[]}})},renderPresentAttendancePieChart:function(e,t,a){new Chart($("#chartjs-present-attendance-pie"),{type:"pie",data:{labels:e,datasets:[{data:t,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(e,t,n)=>{var o=e.chart.width,s=e.chart.height,r=e.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,l=Math.round((o-r.measureText(i).width)/2),c=s/2;r.fillText(i,l,c),r.save()}}]})},renderTransportAssignedPieChart:function(e,t,a){new Chart($("#chartjs-active-transport-pie"),{type:"pie",data:{labels:e,datasets:[{data:t,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderWidth:1,borderColor:window.theme.white}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(e,t,n)=>{var o=e.chart.width,s=e.chart.height,r=e.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,l=Math.round((o-r.measureText(i).width)/2),c=s/2;r.fillText(i,l,c),r.save()}}]})},renderStaffGenderCountChart:function(e,t,a){Chart.Legend.prototype.afterFit=function(){this.height=this.height+20};var n=new Chart($("#chartjs-genderwise-staff-bar-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Male Staff",backgroundColor:LOGO_COLOR,borderColor:LOGO_COLOR,hoverBackgroundColor:LOGO_COLOR,hoverBorderColor:LOGO_COLOR,data:t,barPercentage:.325,categoryPercentage:.5},{label:"Female Staff",backgroundColor:OLD_STUDENTS_FEMALE_COLOR,borderColor:OLD_STUDENTS_FEMALE_COLOR,hoverBackgroundColor:OLD_STUDENTS_FEMALE_COLOR,hoverBorderColor:OLD_STUDENTS_FEMALE_COLOR,data:a,barPercentage:.325,categoryPercentage:.5}]},options:{responsive:!window.MSInputMethodContext,maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0,position:"top",align:"start",labels:{boxWidth:12}},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}});n.canvas.parentNode.style.height="311px",n.canvas.parentNode.style.width="311px"},renderStudentFeesCountChart:function(e,t,a,n,o){new Chart($("#chartjs-institute-fee-distribution"),{type:"bar",data:{labels:e,datasets:[{label:"Collected Fees",backgroundColor:window.theme.success,borderColor:window.theme.success,hoverBackgroundColor:window.theme.success,hoverBorderColor:window.theme.success,data:a,barPercentage:.325,categoryPercentage:.5},{label:"Discounted Fees",backgroundColor:window.theme.warning,borderColor:window.theme.warning,hoverBackgroundColor:window.theme.warning,hoverBorderColor:window.theme.warning,data:n,barPercentage:.325,categoryPercentage:.5},{label:"Due Fees",backgroundColor:window.theme.danger,borderColor:window.theme.danger,hoverBackgroundColor:window.theme.danger,hoverBorderColor:window.theme.danger,data:o,barPercentage:.325,categoryPercentage:.5}]},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderStudentFeeHeadsCountChart:function(e,t,a){const n=["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],o=t.map((t,o)=>{const s=n[o%n.length];return{label:t.split(":")[1],backgroundColor:s,borderColor:s,hoverBackgroundColor:s,hoverBorderColor:s,data:e.map(e=>a[e][t]||0),barPercentage:.325,categoryPercentage:.5}});new Chart($("#chartjs-institute-fee-head-distribution"),{type:"bar",data:{labels:e,datasets:o},options:{maintainAspectRatio:!1,cornerRadius:15,legend:{display:!0},scales:{yAxes:[{ticks:{beginAtZero:!0},gridLines:{display:!1},stacked:!1,stacked:!0}],xAxes:[{stacked:!1,gridLines:{color:"transparent"},stacked:!0}]}}})},renderStaffTodayAttendanceChart:function(e,t,a){new Chart($("#chartjs-staff-present-attendance-pie"),{type:"doughnut",data:{labels:e,datasets:[{data:t,backgroundColor:["#2e7078","#43a2ad","#7fadb2","#a5d4d9","#afdbe0","#e7f9fb"],borderColor:"transparent"}]},options:{rotation:1*Math.PI,circumference:1*Math.PI,maintainAspectRatio:!1,cutoutPercentage:70,legend:{display:!1}},plugins:[{id:"customPlugin",beforeDraw:(e,t,n)=>{var o=e.chart.width,s=e.chart.height,r=e.chart.ctx;r.restore();var d=(s/114).toFixed(2);r.font=d+"em sans-serif",r.textBaseline="middle";var i=a,l=Math.round((o-r.measureText(i).width)/2),c=s/2;r.fillText(i,l,c),r.save()}}]})},generateBirthdayCertificate:function(e){var t=JSON.parse($(e).find(".student-info").text()),a=t.instituteId,n=t.studentAcademicSessionInfoResponse.academicSession.academicSessionId,o=t.studentId;window.open(baseURL+"/organisation-portal/generate-birthday-certificate/"+a+"/"+n+"/"+o,"_blank")}};function loadChangePasswordPage(){ajaxClient.get("/organisation-portal/change-password",function(e){$("#main-content").html(e)})}function changePassword(){var e=$("#old-password").val(),t=$("#new-password").val(),a={oldPassword:e,newPassword:t};t==$("#confirm-new-password").val()?ajaxClient.post("/organisation-portal/update-password",{changePasswordInfo:JSON.stringify(a)},function(e){$("#change-password\\.status-modal-container").html(e),$("#change-password-status-modal").modal("toggle"),$("#old-password").val(""),$("#new-password").val(""),$("#confirm-new-password").val("")}):showErrorDialogBox("Password don't match!!")}function formatINRCurrency(e){return null==e||null==e||""==e?0:e=e.toLocaleString("en-IN")}function viewStatistics(){loadHomePage(),$("#sidebar").removeClass("collapsed")}function viewStatisticsV2(){homePageV2.loadHomePage(),$("#sidebar").removeClass("collapsed")}var menuLoader={registerSidebarMenu:function(){menuLoader.registerFeesReportsMenu()},registerFeesReportsMenu:function(){$("#orgFeesReportNav").on("click",function(){orgFeesReport.loadHomePage()})}},orgFeesReport={dataCache:{},loadHomePage:function(){ajaxClient.get("/organisation-portal/fees-reports",function(e){$("#main-content").html(e),initDateWithYearRange("-5:+5",!0),initSelect2("All"),orgFeesReport.initDataCache(),commonUtils.bindCardHoverEvent(),commonUtils.bindReportCardClickEvent(),orgFeesReport.bindGenerateReportEvent(),reportUtils.bindSelectClassCheckboxEvent()})},initDataCache:function(){var e=readJson("#all-sessions"),t=readJson("#selected-academic-session-json");orgFeesReport.dataCache.allSessions=e,$(".report-academic-session").val(t.academicSessionId)},bindGenerateReportEvent:function(){$(".generate-report").on("click",function(){var e=$(this).closest("div.report-field-container");reportUtils.getReportHeadersCSV(e);if(!validateMandatoryFields($(e))){var t="";$(e).find(".report-academic-session option:selected").length>0&&(t=$(e).find(".report-academic-session option:selected").val()),t=""===t?0:t;var a=$(e).find("p.report-type").text().trim(),n="";$(e).find(".reports-student-status").length>0&&(n=$(e).find(".reports-student-status").val().join()),$(this).closest("div.modal").modal("toggle"),window.open(baseURL+"/organisation-portal/fees-generate-report/"+a+"?academic_session_id="+t+"&studentStatus="+n,"_blank")}})}};